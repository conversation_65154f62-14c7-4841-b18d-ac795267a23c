import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { addBaseProject } from '../interfaces/addBaseProject';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ConfigService } from './config.service';

@Injectable({
	providedIn: 'root',
})
export class PostApiService {
	apiUrl: string;
	lmxUrl: string;
	userRoleUrl: string;
	qcSecurityUrl: string;
	bcrUrl: string;
  	infoshareUrl: string;
	productGroupUrl: string;

	constructor(private http: HttpClient, private config: ConfigService) {
		this.apiUrl = `${this.config.getApiUrl()}`;
		this.lmxUrl = `${this.config.getLmxApiUrl()}`;
		this.userRoleUrl = `${this.config.getUserRoleApiUrl()}`;
		this.qcSecurityUrl = `${this.config.getQCSecurityApiUrl()}`;
		this.bcrUrl = `${this.config.getBCRApiUrl()}`;
    	this.infoshareUrl = `${this.config.getInfoshareApiUrl()}`;
		this.productGroupUrl = `${this.config.getProductGroupApiUrl()}`;
	}

	createBaseProject(
		name?: string,
		suffix?: string,
		typeId?: number,
		panelId?: number,
		dataTypeId?: number,
		purposeId?: number,
		periodicityId?: number,
		countryId?: number,
		predecessors?: any,
		productGroups?: [],
		isRelevantForReportingEnabled?: boolean,
		resetCorrectionTypeId?: number,
		isAutoLoad?: boolean,
		sqcMode?: number,
		isAutomatedPriceCheck?: boolean,
	): Observable<addBaseProject> {
		const body = {
			name,
			suffix,
			typeId,
			panelId,
			dataTypeId,
			purposeId,
			periodicityId,
			countryId,
			predecessors,
			productGroups,
			isRelevantForReportingEnabled,
			resetCorrectionTypeId,
			isAutoLoad,
			sqcMode,
			isAutomatedPriceCheck
		};
		return this.http.post<addBaseProject>(this.apiUrl + '/BaseProjects', body);
	}

	getAsyncBaseProjectsByCountryID(countryId: number, panelId: number) {
		const body = {
			countryId,
			panelId
		};
		return this.http.post(this.apiUrl+'/baseprojects/predecessors', body);
	}

	getShortDescriptionForBaseProjectName(periodicityId?: number, productGroupIds?: []): Observable<any> {
		const body = {
			periodicityId,
			productGroupIds,
		};
		return this.http.post(this.productGroupUrl + '/ProductGroups/Description', body);
	}

	getBaseProjectsListForFilter(
		id?: number,
		name?: string | null,
		countryIds?: any,
		productGroupIds?: any,
		periodicityIds?: any,
		panelIds?: any,
		typeIds?: any,
		dataTypeIds?: any,
		purposeIds?: any,
		includeDeleted?: boolean,
		limit?: number,
		startDate?:any,
		endDate?:any,
		baseProjectIds?:any,
		qcProjectIds?:any,
		users?:any
	): Observable<any> {
		const body = {
			id,
			name,
			countryIds,
			productGroupIds,
			periodicityIds,
			panelIds,
			typeIds,
			dataTypeIds,
			purposeIds,
			includeDeleted,
			limit,
			startDate,
			endDate,
			baseProjectIds,
			qcProjectIds,
			users,

		};
		return this.http.post(this.apiUrl + '/BaseProjects/List', body);
	}

	getAsyncDomainProductGroup(sectorIds?: number[], categoryIds?:number[]) {
		const body={ sectorIds,categoryIds };
		return this.http.post(this.lmxUrl+'/DomainProductGroups', body);
	}

	getAsyncProductGroup(panelIds?: number[], countryIds?: number[],sectorIds?: number[],categoryIds?:number[],domainProductGroupIds?:number[]) {
		const body={panelIds, countryIds,sectorIds,categoryIds,domainProductGroupIds};
		return this.http.post(this.productGroupUrl+'/ProductGroups',body);
	}

	getlmxApiCategories( sectorIds?: number[]) {
		const body={sectorIds};
		return this.http.post(this.lmxUrl+'/categories',body);
	}

	createQCPeriod(payload): Observable<any> {
		return this.http.post(this.apiUrl + '/qcperiods/qcPeriod', payload);
	}

	requestUserRoles(payload): Observable<any> {
		return this.http.post(this.userRoleUrl+'/userroles', payload);
	}

	getUserRoleList(payload): Observable<any>{
		return this.http.post(this.userRoleUrl+'/userroles/list', payload);
	}

	createBulkQCPeriod(payload, qcProjectId): Observable<any> {
		return this.http.post(this.apiUrl + '/qcperiods/qcperiod/bulkQCPeriod/'+qcProjectId, payload);
	}

	addUsersForQCSecurity(payload): Observable<any> {
		return this.http.post(this.qcSecurityUrl + '/projectuser', payload);
	}

	baseChannelRearrancementCheck(payload): Observable<any> {
		return this.http.post(this.bcrUrl + '/base-channel-rearrangement/check', payload);
	}

	addRetailerSeparation(
		fromPeriodId?: any,
		toPeriodId?: any,
		resetCorrection?: boolean,
		extrapolation?: boolean,
		retailerSeperations?: any
	): Observable<any> {
		const body = {
			fromPeriodId,
			toPeriodId,
			resetCorrection,
			extrapolation,
			retailerSeperations
		};
		return this.http.post(this.apiUrl + '/retailerseperation', body);
	}

	getRetailerSeparationList(queryParams: { [key: string]: any }): Observable<any> {
		// Filter out keys where the value is null, undefined, empty array, or empty string
		const filteredParams = Object.keys(queryParams).reduce((acc, key) => {
		  const value = queryParams[key];
		  if (value && (Array.isArray(value) ? value.length > 0 : true)) {
			acc[key] = value;
		  }
		  return acc;
		}, {});

		// Send the request with the filteredParams as the body, Angular will default to application/json
		return this.http.post(this.apiUrl + '/retailerseperation/list', filteredParams);
	}

	executeRetailerSeparation(payload): Observable<any> {
		return this.http.post(this.apiUrl + '/retailerseperation/IRSeparation', payload);
	}

	addInfoDetails(payload: any): Observable<any> {
		const url = `${this.infoshareUrl}/infodetails`;
		return this.http.post(url, payload);
	}

	createBulkBaseProjects(payload) {
		return this.http.post(this.apiUrl+'/baseprojects/bulk', payload);
	}

}
