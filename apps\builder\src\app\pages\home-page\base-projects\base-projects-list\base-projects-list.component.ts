import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Output, QueryList, ViewChildren, computed, inject } from '@angular/core';
import { SettingsConstants } from '@builder/shared/settings.constants';
import { Router, RoutesRecognized } from '@angular/router';
import { EdsTr, EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { Observable, Subject, Subscription, filter, finalize, map, pairwise, takeUntil, tap } from 'rxjs';
import { FilterService, FilterRecords, Filter } from '@dwh/dx-lib/src';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { DeleteApiService } from '@builder/shared/services/delete-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { BannerService } from '@builder/shared/services/banner.service';
import { HasMultiSelectTable } from '@builder/shared/components/has-multi-select-table/has-multi-select-table';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UserService } from '@dwh/dx-lib/src/lib/services/auth';
import * as moment from 'moment-timezone';
import { DatePipe } from '@angular/common';
import { PutApiService } from '@builder/shared/services/put-api.service';
import { BaseProjects } from '@builder/shared/interfaces/BaseProjects';

export interface PageChange {
	totalPages: number;
	pageIndex: number;
	pageSize: number;
	previousPageIndex: number;
}

export interface AutocompleteOption {
	value: string;
	label: string;
}
@Component({
	selector: 'dx-base-projects-list',
	templateUrl: './base-projects-list.component.html',
	styleUrls: ['./base-projects-list.component.scss'],
})
export class BaseProjectsListComponent extends HasMultiSelectTable<any>  implements OnInit, OnDestroy {

	@ViewChildren(EdsTr) _allRows!: QueryList<EdsTr>;
	allRows = computed(() => this.filterBodyRows(this._allRows.toArray()));
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);
	private readonly userService = inject(UserService);

	@ViewChildren(EdsTr) selectedRows!: QueryList<EdsTr>;

	panels$!: Observable<AutocompleteOption[]>;
	countries$!: Observable<AutocompleteOption[]>;
	periodicities$!: Observable<AutocompleteOption[]>;
	projectSubType$!: Observable<AutocompleteOption[]>;
	dataType$!: Observable<AutocompleteOption[]>;
	purpose$!: Observable<AutocompleteOption[]>;
	displayedColumns: string[] = ['id', 'name', 'typeId', 'panelId', 'countryId', 'productGroups', 'periodicityId'];
	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITHOUT_TIME;
	icon = 'help';
	baseProjectData!: any;
	visibleBaseProjects: any[] = [];
	resetForm: Subject<boolean> = new Subject();
	IsDisabled = false;
	IsEnabled = true;
	FilterTrayOpenFlag = false;
	eventsSubject: Subject<void> = new Subject<void>();
	filterCount = '';
	@Output() isShownFilterTray = new EventEmitter<any>();
	selectedFilters: any;
	isLoading = false;
	chips: any = [];
	inputValue = '';
	errorIcon = 'error';
	paginatedBaseProjects: any[] = [];
	panelList: any = [];
	baseProjectTypeList: any = [];
	countriesList: any = [];
	periodicityData: any = [];
	productGroupData: any = [];
	dataTypeData: any = [];
	filteredDataTypeData: any = [];
	purposeData: any = [];
	deleteConfirmationModal!: boolean;
	deleteValidationModal!: boolean;
	productGroup$!: Observable<AutocompleteOption[]>;
	allproductGroups: any[] = [];
	isBannerVisible: any;

	pageSize: any;
	currentPage!: number;
	readonly defaultPageSize = 10;
	readonly pageSizeOptions: number[] = [10, 25, 50, 100];
	showTray!: boolean;
	setPageNo: any;
	filterApplied!: boolean;
	userRole: any;
	validationMessage: any;
	displayNonDeletedMessage = false;
	displayDeleteWithErrorMessage = false;
	bpDeleteConflictDetails: any;
	deletedBps: any = [];
	subscription!: Subscription;
	addQCSecurityUsersModal!: boolean;
	addQCSecurityUsersForm!: FormGroup;
	restoreConfirmationModal!: boolean;

	addBPSecurityUsersModal!: boolean;
	addBPSecurityUsersForm!: FormGroup;

	users$!: Observable<any[]>;
	userList!: any[];
	disableAddUserBtn!: boolean;
	userSearchInterval: any;
	loadBaseProjectData!: boolean;
	timezone = moment.tz.guess();
	baseProjectUserList: any=[];
	allDeletedBPSelected = false;

	createBulkBPModal!: boolean;
	selectedBPIdsForBulkCreation!: string;
	copiedBPModal!: boolean;
	copiedBaseProjectList!: any;
	editBulkBPModal!: boolean;
	selectedPeriodicityName!: string | null;
	editBulkBPForm!: FormGroup;
	disableEditBtn = true;

	private destroy$ = new Subject<void>();

	getId({ id }: any): number {
		return id;
	}

	constructor(
		private router: Router,
		private getApiService: GetApiService,
		private deleteApiService: DeleteApiService,
		private postApiService: PostApiService,
		public filterService: FilterService,
		private bannerService: BannerService,
		protected cdRef: ChangeDetectorRef,
		private formBuilder: FormBuilder,
		public datepipe: DatePipe,
		private putApiService: PutApiService

	) {
		super(cdRef);
		this.pageSize = this.defaultPageSize;
	}

	ngOnInit(): void {
		this.getUsersList();
		this.subscription = this.getApiService.getUserRoleSubject().subscribe((userRole) => {
			this.userRole = userRole;
			this.getPanelList();
			this.getBaseProjectsTypeList();
			this.getBaseProjectsUserList();
			this.getCountriesList();
			this.getPeriodicitiesList();
			this.getDataTypeList();
			this.getPurposeList();
			this.bannerService.isVisible$.subscribe((response: any) => {
				this.isBannerVisible = response;
			});
			this.router.events.pipe(filter((e: any) => e instanceof RoutesRecognized),pairwise()).subscribe((e: any) => {
				if(e[0].urlAfterRedirects){
					localStorage.setItem('previousUrl', JSON.stringify(e[0].urlAfterRedirects));
				}
			});
			const baseProjectPageNo = localStorage.getItem('baseProjectPageNo') == 'undefined' ||  localStorage.getItem('baseProjectPageNo') === null ? '' : JSON.parse(localStorage.getItem('baseProjectPageNo')!);
			const previousUrl = JSON.parse(localStorage.getItem('previousUrl')!);
			const selectedFiltersonNavigation = JSON.parse(localStorage.getItem('selectedFiltersonNavigation')!);
			if(baseProjectPageNo){
				if(previousUrl && (previousUrl.indexOf('create') > -1) && !selectedFiltersonNavigation){
					this.currentPage = 1;
				}
				else{
					this.currentPage = baseProjectPageNo;
				}
			}
			else{
				this.currentPage = 1;
			}
			if(baseProjectPageNo){
				this.selectedFilters = selectedFiltersonNavigation;
				if(selectedFiltersonNavigation){
					this.CreateFilterSummaryChips();
					this.SetFiltersCount();
					this.setChips();
				}
				if(previousUrl){
					setTimeout(() => {
						this.getBaseProjectsList();
					}, 100)
				}
			}
		});
		this.editBulkBPFormInitialization();
	}

	ngOnDestroy() {
		this.subscription?.unsubscribe();
		this.destroy$.next();
		this.destroy$.complete();
	}

	getPanelList(){
		this.panels$ = this.getApiService.getAsyncPanel()
			.pipe(
				map((panelList: any) => panelList.map((panel: any) => ({ label: panel.name, value: (panel.id).toString() } as AutocompleteOption)))
			);
		this.panels$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.panelList = result;
			}
		});
	}

	getBaseProjectsTypeList(){
		this.projectSubType$ = this.getApiService.getAsyncProjectSubType()
			.pipe(
				map((projectSubTypes: any) =>
					projectSubTypes.map(
						(projectSubType: any) => ({ label: projectSubType.name, value: projectSubType.id?.toString() } as AutocompleteOption)
					)
				)
			);
		this.projectSubType$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.baseProjectTypeList = result.filter((x) => x.value == '1' || x.value == '2' || x.value == '3');
			}
		});
	}

	getBaseProjectsUserList(){
		this.projectSubType$ = this.getApiService.getAsyncBaseProjectUsers()
			.pipe(
				map((usernames: any) =>
					usernames.map(
						(usernames: any) => ({ label: usernames, value: usernames } as AutocompleteOption)
					)
				)
			);
		this.projectSubType$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.baseProjectUserList = result;
			}
		});
	}

	getCountriesList() {
		this.countries$ = this.getApiService.getCountries()
			.pipe(
				map((countries: any) =>
					countries.map((country: any) => ({ label: country.name, value: country.id.toString() } as AutocompleteOption))
				)
			);
		this.countries$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.countriesList = result;
			}
		});
	}

	getPeriodicitiesList() {
		this.periodicities$ = this.getApiService.getAsyncPeriodicities()
			.pipe(
				map((periodicities: any) =>
					periodicities.map((periodicity: any) => ({ label: periodicity.name, value: periodicity.id.toString() } as AutocompleteOption))
				)
			);
		this.periodicities$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.periodicityData = this.sortPeriodicities(result);
			}
		});
	}

	/**
	 * Sort periodicities in ascending range-wise order: daily, weekly, monthly, 2 monthly, 3 monthly, 4 monthly, etc.
	 * Within each group, sort by starting month: Jan, Feb, Mar, etc.
	 */
	private sortPeriodicities(periodicities: AutocompleteOption[]): AutocompleteOption[] {
		return periodicities.sort((a, b) => {
			const orderA = this.getPeriodicityOrder(a.label);
			const orderB = this.getPeriodicityOrder(b.label);

			if (orderA !== orderB) {
				return orderA - orderB;
			}
			const monthOrderA = this.getStartingMonthOrder(a.label);
			const monthOrderB = this.getStartingMonthOrder(b.label);

			if (monthOrderA !== monthOrderB) {
				return monthOrderA - monthOrderB;
			}
			return a.label.localeCompare(b.label);
		});
	}


	private getPeriodicityOrder(label: string): number {
		const lowerLabel = label.toLowerCase();

		if (lowerLabel.includes('daily')) {
			return 1;
		}

		if (lowerLabel.includes('weekly')) {
			return 7;
		}

		if (lowerLabel.includes('monthly')) {
			const monthMatch = lowerLabel.match(/(\d+)-monthly/);
			if (monthMatch) {
				const monthCount = parseInt(monthMatch[1], 10);
				return monthCount * 30; // 2-monthly = 60, 3-monthly = 90, etc.
			}
			return 30;
		}
		if (lowerLabel.includes('yearly')) {
			return 360;
		}
		return 1000;
	}

	/**
	 * Get the sort order for starting month within the same periodicity group
	 */
	private getStartingMonthOrder(label: string): number {
		const lowerLabel = label.toLowerCase();

		const monthMatch = lowerLabel.match(/start\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/);
		if (monthMatch) {
			const monthMap: { [key: string]: number } = {
				'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
				'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
			};
			return monthMap[monthMatch[1]] || 0;
		}
		return 0;
	}

	getDataTypeList(){
		this.dataType$ = this.getApiService.getAsyncDataType()
			.pipe(
				map((dataTypes: any) => dataTypes.map((dataType: any) => ({ label: dataType.name, value: (dataType.id).toString() } as AutocompleteOption)))
			);
		this.dataType$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.dataTypeData = this.sortDataTypes(result);
			}
		});
	}

	sortDataTypes(dataTypes: AutocompleteOption[]): AutocompleteOption[] {
		const dataTypeOrder = ['FW', 'WC', 'MC', 'ME', 'LP', 'LPAC', 'LPWC', 'MP', 'MD', 'WE', 'Unknown'];

		return dataTypes.sort((a, b) => {
			const indexA = dataTypeOrder.indexOf(a.label);
			const indexB = dataTypeOrder.indexOf(b.label);

			if (indexA !== -1 && indexB !== -1) {
				return indexA - indexB;
			}

			if (indexA !== -1) return -1;
			if (indexB !== -1) return 1;

			return a.label.localeCompare(b.label);
		});
	}

	getPurposeList(){
		this.purpose$ = this.getApiService.getAsyncPurpose()
			.pipe(
				map((purposeList: any) => purposeList.map((purpose: any) => ({ label: purpose.name, value: (purpose.id).toString() } as AutocompleteOption)))
			);
		this.purpose$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.purposeData = result;
			}
		});
	}

	getBaseProjectsList() {
		this.getFilteredBaseProjects();
	}

	navigationToUpdateBaseProject() {
		this.router.navigate(['/base-projects/update/' + this.selectedEntities[0].id]);
	}

	navigationToCopyBaseProject() {
		this.router.navigate(['/base-projects/copy/' + this.selectedEntities[0].id]);
	}

	openDeleteConfirmationModal() {
		this.deleteConfirmationModal = true;
	}

	openRestoreConfirmationModal() {
		this.restoreConfirmationModal = true;
	}

	/**
	 * @name deleteSelectedBaseProject
	 * @desc deletes selected base project and also checks for validation for any linked base projects
	*/
	deleteSelectedBaseProject(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.deleteConfirmationModal = false;
		}
		else{
			this.loadBaseProjectData = true;
			this.initializeDeleteState();
			const selectedBPIds = this.getSelectedBaseProjectIds();
			this.deleteConfirmationModal = false;
			this.deleteApiService.deleteSelectedBaseProjects({ ids: selectedBPIds }).subscribe({
				next: (result: any) => this.handleDeleteResponse(result, selectedBPIds),
				error: (error) => this.handleDeleteError(error),
			});
		}
	}

	initializeDeleteState(): void {
		this.deletedBps = [];
		this.displayNonDeletedMessage = false;
		this.validationMessage = '';
		this.displayDeleteWithErrorMessage = false;
	}

	getSelectedBaseProjectIds(): Array<number> {
		return this.selectedEntities.map((item: any) => item.id);
	}

	handleDeleteResponse(result: any, selectedBPIds: Array<number>): void {
		if (this.areAllDeleted(result)) {
			this.handleSuccessfulDeletion(result, selectedBPIds);
		} else {
			this.handlePartialDelete(result);
		}
	}

	areAllDeleted(result: any): boolean {
		return result.every((response: any) => response.statusCode === 200);
	}

	handleSuccessfulDeletion(result: any, selectedBPIds: Array<number>): void {
		this.getBaseProjectsList();
		this.clearSelection();
		const title = selectedBPIds.length == 1 ? 'Base project deleted' : `${selectedBPIds.length} Base projects deleted`;
		const message = selectedBPIds.length == 1 ? `BP ID: ${selectedBPIds[0]}` : '';
		this.notifyWidget(title, 'success', message);
	}

	handlePartialDelete(result: any): void {
		if (this.areAllNonDeleted(result)) {
			this.displayNonDeletedMessage = true;
			this.bpDeleteConflictDetails = result;
			this.loadBaseProjectData = false;
		} else {
			this.displayDeleteWithErrorMessage = true;
			this.processDeleteResults(result);
		}
		this.openDeleteValidationModal();
	}

	areAllNonDeleted(result: any): boolean {
		return result.every((response: any) => response.statusCode === 400);
	}

	processDeleteResults(result: any): void {
		this.bpDeleteConflictDetails = [];
		this.deletedBps = [];
		result.forEach((item: any) => {
			if (item.statusCode === 200) {
				this.deletedBps.push(item.baseProjectId);
			} else {
				this.bpDeleteConflictDetails.push(item);
			}
		});
		this.loadBaseProjectData = false;
	}

	handleDeleteError(error: any): void {
		const errorCodes = [400, 401, 404, 500, 502, 503, 504];
		let title = '';
		if (errorCodes.includes(error.status)) {
			title = `Error: ${error.status}. Unable to delete selected base project(s). Please contact administrator.`;
			this.notifyWidget(title, 'error');
		}
		this.loadBaseProjectData = false;
	}

	openDeleteValidationModal() {
		this.deleteValidationModal = true;
	}

	closeDeleteValidationModal() {
		if(this.deletedBps.length){
			this.clearSelection();
			this.getBaseProjectsList();
		}
		this.deleteValidationModal = false;
	}

	getSelectedFilters(selectedFilters: any) {
		this.selectedFilters = selectedFilters;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	confirmApply(pageNo: number) {
		this.setPageNo = pageNo;
		this.CreateFilterSummaryChips();
		const includeDeleted = this.selectedFilters?.includeDeletedBaseProjects;
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		this.SetFiltersCount();
		this.setChips();
		if (count > 0 || this.inputValue || includeDeleted) {
			this.refresh();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.refresh();
			}
		}
	}

resetFilterTray() {
		this.resetForm.next(true);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
		this.removeLocalStorageItems();
	}

// 	resetFilterTray() {
// // Ask child to reset its form state
// 		this.resetForm.next(true);
// 		// Also clear the parent copy immediately to avoid any stale values on next Apply
// 		this.selectedFilters = {
// 			baseProjectPanel: [],
// 			baseProjectDataType: [],
// 			baseProjectPurpose: [],
// 			baseProjectCountry: [],
// 			baseProjectDomainProductGroup: [],
// 			baseProjectProductGroup: [],
// 			baseProjectSector: [],
// 			baseProjectCategory: [],
// 			baseProjectPeriodicity: [],
// 			baseProjectSubType: [],
// 			baseProjectType: [],
// 			baseProjectStartDate: null,
// 			baseProjectEndDate: null,
// 			baseProjectIds: null,
// 			qcProjectIds: null,
// 			baseProjectUsers: null,
// 			baseProjectHistory: [],
// 			includeDeletedBaseProjects: false
// 		};
// 		this.CreateFilterSummaryChips();
// 		this.SetFiltersCount();
// 		this.removeLocalStorageItems();
// 	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.showTray = !this.showTray;
		this.FilterTrayOpenFlag = $event;
		const element = document.getElementById('sidebar-modal') as HTMLElement;
		if(this.isBannerVisible){
			element.classList.add('sidebar-modal-color-with-banner');
			element.classList.remove('sidebar-modal-color-without-banner');
		}
		else{
			element.classList.remove('sidebar-modal-color-with-banner');
			element.classList.add('sidebar-modal-color-without-banner');
		}
	}

	onCloseFilterTray() {
		this.getWidth();
        this.showTray = false;
    }
    onOpenFilterTray() {
        this.showTray = true;
    }

	CreateFilterSummaryChips() {
		if (this.selectedFilters && Object.keys(this.selectedFilters).length) {
			this.panelFilterSummary();
			this.dataTypeFilterSummary();
			this.purposeFilterSummary();
			this.countryFilterSummary();
			this.sectorFilterSummary();
			this.categoryFilterSummary();
			this.productGroupFilterSummary();
			this.periodicityFilterSummary();
			this.baseProjectSubTypeFilterSummary();
			this.baseProjectTypeFilterSummary();
			this.baseProjectUsersFilterSummary();
			this.baseProjectIdsFilterSummary();
			this.qcProjectIdsFilterSummary();
			this.baseProjectHistoryFilterSummary();
			this.includeDeletedFilterSummary();
			//this.datesFilterSummary(this.selectedFilters.baseProjectStartDate, this.selectedFilters.baseProjectEndDate);
		}
	}

	SetFiltersCount() {
		// Compute count directly from the current selectedFilters snapshot to avoid async store timing
		if (!this.selectedFilters) {
			this.filterCount = '';
			return;
		}
		let count = 0;
		const filterKeys = [
			'baseProjectPanel',
			'baseProjectDataType',
			'baseProjectPurpose',
			'baseProjectCountry',
			'baseProjectSector',
			'baseProjectCategory',
			'baseProjectDomainProductGroup',
			'baseProjectPeriodicity',
			'baseProjectType',
			'baseProjectSubType',
			'baseProjectUsers',
			'baseProjectHistory'
		];
		for (const key of filterKeys) {
			const val = this.selectedFilters[key];
			if (Array.isArray(val) && val.length > 0) count++;
		}
		// String-based ID fields
		if (this.selectedFilters.baseProjectIds && typeof this.selectedFilters.baseProjectIds === 'string') {
			const ids = this.selectedFilters.baseProjectIds.split(',').filter((x: string) => x.trim() !== '');
			if (ids.length > 0) count++;
		}
		if (this.selectedFilters.qcProjectIds && typeof this.selectedFilters.qcProjectIds === 'string') {
			const ids = this.selectedFilters.qcProjectIds.split(',').filter((x: string) => x.trim() !== '');
			if (ids.length > 0) count++;
		}
		// Boolean include-deleted
		if (this.selectedFilters.includeDeletedBaseProjects) count++;
		this.filterCount = count > 0 ? `(${count})` : '';
	}

	panelFilterSummary() {
		this.filterService.setFilters({
			panel: {
				name: 'Panel',
				values: this.selectedFilters.baseProjectPanel,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	dataTypeFilterSummary() {
		this.filterService.setFilters({
			dataType: {
				name: 'DataType',
				values: this.selectedFilters.baseProjectDataType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	purposeFilterSummary() {
		this.filterService.setFilters({
			purpose: {
				name: 'Purpose',
				values: this.selectedFilters.baseProjectPurpose,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	countryFilterSummary() {
		this.filterService.setFilters({
			country: {
				name: 'Country',
				values: this.selectedFilters.baseProjectCountry,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	sectorFilterSummary() {
		this.filterService.setFilters({
			sector: {
				name: 'Sector',
				values: this.selectedFilters.baseProjectSector,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	categoryFilterSummary() {
		this.filterService.setFilters({
			category: {
				name: 'Category',
				values: this.selectedFilters.baseProjectCategory,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	productGroupFilterSummary() {
		this.filterService.setFilters({
			productGroup: {
				name: 'ProductGroup',
				values: this.selectedFilters.baseProjectProductGroup,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	periodicityFilterSummary() {
		this.filterService.setFilters({
			periodicity: {
				name: 'Periodicity',
				values: this.selectedFilters.baseProjectPeriodicity,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectSubTypeFilterSummary() {
		this.filterService.setFilters({
			baseProjectSubType: {
				name: 'BaseProjectSubType',
				values: this.selectedFilters.baseProjectSubType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectTypeFilterSummary() {
		this.filterService.setFilters({
			baseProjectType: {
				name: 'BaseProjectType',
				values: this.selectedFilters.baseProjectType,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectIdsFilterSummary() {
		this.filterService.setFilters({
			baseProjectIds: {
				name: 'BaseProjectIds',
				values: this.selectedFilters.baseProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectUsersFilterSummary() {
		this.filterService.setFilters({
			baseProjectUsers: {
				name: 'BaseProjectIds',
				values: this.selectedFilters.baseProjectUsers,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	qcProjectIdsFilterSummary() {
		this.filterService.setFilters({
			qcProjectIds: {
				name: 'QCProjectIds',
				values: this.selectedFilters.qcProjectIds,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	baseProjectHistoryFilterSummary() {
		this.filterService.setFilters({
			baseProjectHistory: {
				name: 'BaseProjectHistory',
				values: this.selectedFilters.baseProjectHistory,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	datesFilterSummary(dateStart: any, dateEnd: any) {
		if (dateStart != null || dateEnd != null) {
			const start = dateStart == 'Invalid Date' || dateStart == null ? '' : this.datepipe.transform(dateStart, 'yyyy-MM-dd');
			const end = dateEnd == 'Invalid Date' || dateEnd == null ? '' : this.datepipe.transform(dateEnd, 'yyyy-MM-dd');
			this.filterService.setFilters({
				baseProjectStartDate: {
					name: 'baseProjectStartDate',
					values: (start !== undefined && end !== undefined) ? [start, end] : null,
					slice: window.location.href.split('/')[3],
				} as Filter,
			});
		} else {
			this.filterService.setFilters({
				baseProjectStartDate: {
					name: 'baseProjectStartDate',
					values: null,
					slice: window.location.href.split('/')[3],
				} as unknown as Filter,
			});
		}
	}

	closeClick() {
		this.inputValue = '';
	}

	refresh(): void {
		this.showTray = false;
		this.getWidth();
		if (
			this.selectedFilters?.baseProjectProductGroup && this.selectedFilters?.baseProjectProductGroup.length == 0 &&
			(this.selectedFilters?.baseProjectCategory.length != 0 || this.selectedFilters?.baseProjectSector.length != 0)
		) {
			this.getProductGroup(
				this.selectedFilters?.baseProjectPanel.length == 0 ? [1,2,3] : this.selectedFilters.baseProjectPanel,
				this.selectedFilters.baseProjectCountry,
				this.selectedFilters.baseProjectSector,
				this.selectedFilters.baseProjectCategory,
				this.selectedFilters.baseProjectDomainProductGroup
			);
		} else {
			this.getFilteredBaseProjects();
		}
		this.isLoading = true;
	}

	onInputChange(value: string): void {
		this.inputValue = value;
	}

	getProductGroup(panel: any, country: any, sector: any, category: any, domainProductGroup: any) {
		this.productGroup$ = this.postApiService.getAsyncProductGroup(panel, country, sector, category, domainProductGroup).pipe(
			map((productGroups: any) =>
				productGroups.map((productGroup: any) => ({
					label: productGroup.description + ' (' + productGroup.id + ')',
					value: productGroup.id.toString(),
				}))
			)
		);
		this.productGroup$
		.pipe(
			tap((result) => {
				this.selectedFilters.baseProjectProductGroup = result.length === 0 ? [] : result.map((x) => x.value);
			}),
			finalize(() => {
				this.getFilteredBaseProjects();
			})
		)
		.subscribe({
			error: (error) => {
				const title = error?.message == null ? error?.error : error?.message;
				// this.notifyWidget(title, 'error');
			}
		});
		// this.productGroup$.subscribe({
		// 	next: (result) => {
		// 		this.selectedFilters.baseProjectProductGroup = result.map((x) => x.value);
		// 		this.getFilteredBaseProjects();
		// 	},
		// 	error: (error) => {
		// 		const title = error?.message == null ? error?.error : error?.message;
		// 		this.notifyWidget(title, 'error');
		// 	},
		// });
	}

	includeDeletedFilterSummary() {
		this.filterService.setFilters({
			includeDeletedBaseProjects: {
				name: 'IncludeDeletedBaseProjects',
				values: this.selectedFilters?.includeDeletedBaseProjects ? ['true'] : null,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	getFilteredBaseProjects() {
		this.loadBaseProjectData = true;
		if(this.selectedFilters){
			localStorage.setItem('selectedFiltersonNavigation', JSON.stringify(this.selectedFilters));
		}
		localStorage.setItem('baseProjectPageNo', JSON.stringify(this.currentPage));
		const parsedValue = this.parseInputValue(this.inputValue);
		const productGroupIds = (this.selectedFilters?.baseProjectDomainProductGroup && this.selectedFilters.baseProjectDomainProductGroup.length > 0)
			? this.selectedFilters.baseProjectProductGroup
			: null;
		this.postApiService.getBaseProjectsListForFilter(
			parsedValue,
			this.inputValue.trim() || null,
			this.selectedFilters?.baseProjectCountry,
			productGroupIds,
			this.selectedFilters?.baseProjectPeriodicity,
			this.selectedFilters?.baseProjectPanel,
			this.selectedFilters?.baseProjectType,
			this.selectedFilters?.baseProjectDataType,
			this.selectedFilters?.baseProjectPurpose,
        	this.selectedFilters?.includeDeletedBaseProjects ?? false,
			1000,
			this.selectedFilters?.baseProjectHistory && this.selectedFilters.baseProjectHistory.length !== 0
			?  moment(this.selectedFilters.baseProjectStartDate).format().split('T')[0] + 'T00:00:00.000Z' : null,
			this.selectedFilters?.baseProjectHistory && this.selectedFilters.baseProjectHistory.length !== 0
			?  moment(this.selectedFilters.baseProjectEndDate).format().split('T')[0] + 'T23:59:59.999Z' : null,
			this.selectedFilters?.baseProjectIds!=null && this.selectedFilters.baseProjectIds!=''? 
			this.selectedFilters?.baseProjectIds.split(",").map(Number):[],
			this.selectedFilters?.qcProjectIds!=null && this.selectedFilters.qcProjectIds!=''?
			this.selectedFilters?.qcProjectIds.split(",").map(Number):[],
			this.selectedFilters?.baseProjectUsers
		).subscribe({
			next: (result: any) => this.handleBaseProjectsResponse(result),
			error: (error) => this.handleError(error),
		});
	}

	parseInputValue(input) {
		if (input === '') return 0;
		if (/^\d+$/.test(input.trim())) return parseInt(input);
		return -1;
	}

	handleBaseProjectsResponse(result: any) {
		if (result.moreRecordsAvailable) {
			const title = 'More than 1000 results exist!';
			this.notifyWidget(title, 'info');
		}
		result.records.forEach((item: any, index: any) => {
		if(item.updatedWhen){
			const date = new Date(item.updatedWhen.toString());
			const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, item.updatedWhen);
			const cetTime = moment.utc(date).tz(this.timezone).format('YYYY-MM-DD HH:mm');
			item.updatedWhenCET = cetTime + ' ' + timeZoneAbbreviation;
		}
		})
		let countryIds: any = [];
		const productGroupData: any = JSON.parse(localStorage.getItem('productGroupData') || '[]');
		localStorage.removeItem('previousUrl');
		countryIds = this.extractUniqueCountryIds(result.records);
		this.updateProductGroupData(countryIds, productGroupData, result);
	}

	onRestoreBaseProject(): void {
		if (!this.selectedEntities || this.selectedEntities.length === 0) {
			this.notifyWidget('No Base Projects selected', 'error');
			return;
		}

		this.loadBaseProjectData = true;
		let restoreCount = 0;

		this.selectedEntities.forEach((bp: any, index: number) => {
			// Fetch complete base project data to get current product groups and predecessors
			this.getApiService.getBaseProjectsByBaseProjectId(bp.id).subscribe({
				next: (fullBpData: any) => {
					console.log('Full BP data received:', fullBpData);

					// Extract product group IDs from the full data structure
					const currentProductGroups = fullBpData.productGroups ?
						fullBpData.productGroups.map((pg: any) => ({ productGroupId: pg.productGroupId })) : [];

					// Extract predecessor IDs from the full data structure
					const currentPredecessors = fullBpData.predecessors ?
						fullBpData.predecessors.map((pred: any) => ({ predecessorId: pred.predecessorId })) : [];

					console.log('Restoring BP:', bp.id);
					console.log('Product Groups:', currentProductGroups);
					console.log('Predecessors:', currentPredecessors);
					console.log('Payload will be:', {
						name: fullBpData.name,
						suffix: fullBpData.suffix,
						productGroups: currentProductGroups,
						predecessors: currentPredecessors,
						dataTypeId: fullBpData.dataTypeId,
						purposeId: fullBpData.purposeId,
						isRelevantForReportingEnabled: fullBpData.isRelevantForReportingEnabled,
						deleted: false
					});

					this.putApiService.updateBaseProject(
						bp.id,
						fullBpData.name,
						fullBpData.suffix,
						currentProductGroups,
						currentPredecessors,
						fullBpData.dataTypeId,
						fullBpData.purposeId,
						fullBpData.isRelevantForReportingEnabled,
						false // <- the key to restore: set Deleted = false
					).subscribe({
						next: () => {
							restoreCount++;
							if (restoreCount === this.selectedEntities.length) {
								this.notifyWidget('Selected Base Projects restored successfully', 'success');
								this.getBaseProjectsList(); // Refresh table
								this.clearSelection(); // Clear selection
								this.loadBaseProjectData = false;
								this.restoreConfirmationModal = false;
							}
						},
						error: (error) => {
							const title = 'Error restoring base project ID: ' + bp.id;
							this.notifyWidget(title, 'error');
							this.loadBaseProjectData = false;
						}
					});
				},
				error: (error) => {
					const title = 'Error fetching base project details for ID: ' + bp.id;
					this.notifyWidget(title, 'error');
					this.loadBaseProjectData = false;
				}
			});
		});
	}


	extractUniqueCountryIds(records: any[]): number[] {
		const countryIds = new Set<number>();
		records.forEach(record => {
			countryIds.add(record.countryId);
		});
		return Array.from(countryIds);
	}

	updateProductGroupData(countryIds: number[], productGroupData: any, result: any) {
		countryIds = [...productGroupData.countryIds, ...countryIds];
		countryIds = [...new Set(countryIds)];
		const newCountryIds = productGroupData.countryIds.filter(x => !countryIds.includes(x)).concat(countryIds.filter(x => !productGroupData.countryIds.includes(x)));
		this.baseProjectData = result.records;
		this.productGroupData = productGroupData.productGroupList;
		if(this.setPageNo){
			this.currentPage = this.setPageNo;
			this.setPageNo = null;
		}
		if (newCountryIds.length) {
			this.postApiService.getAsyncProductGroup([1, 2, 3], newCountryIds, [], [], []).subscribe({
				next: (result) => {
					this.productGroupData = result;
					this.productGroupData = [...productGroupData.productGroupList, ...this.productGroupData];
					this.productGroupData = [...new Set(this.productGroupData)];
					this.saveProductGroupData(countryIds);
				},
				error: (error) => this.handleErrorInSuccessResponse(error),
			});
		}
		else {
			this.productGroupData = productGroupData.productGroupList;
		}
		this.visibleBaseProjects = this.getPageBaseProjects(this.currentPage, this.pageSize);
		this.loadBaseProjectData = false;
		this.cdRef.markForCheck();
	}

	saveProductGroupData(countryIds: number[]) {
		const newProductGroupData = {
			countryIds: countryIds,
			productGroupList: this.productGroupData
		}
		localStorage.setItem('productGroupData', JSON.stringify(newProductGroupData));
	}

	handleError(error: any) {
		if (error.status === 404) {
			this.visibleBaseProjects = [];
			this.baseProjectData = null;
			if(this.selectedFilters || this.inputValue){
				this.filterApplied = true;
			}
		}
		else {
			const errorCodes = [400, 401, 500, 502, 503, 504];
			if (errorCodes.includes(error.status)) {
				const title = `Error: ${error.status}. Unable to fetch Base Project. Please contact administrator.`;
				this.notifyWidget(title, 'error');
			}
		}
		this.loadBaseProjectData = false;
	}

	handleErrorInSuccessResponse(error) {
		const title = this.getErrorTitleForBPList(error);
		this.notifyWidget(title, 'error');
	}

	getErrorTitleForBPList(error: any) {
		const errorCodes = [400, 401, 500, 502, 503, 504];
		if (errorCodes.includes(error.status)) {
			return `Error: ${error.status}. Unable to fetch Product Groups. Please contact administrator.`;
		}
		return error?.message == null ? error?.error : error?.message;
	}

	setChips() {
		this.chips = [];
		if (!this.selectedFilters) return;

		const filterMapping = {
			baseProjectPanel: 'Panel',
			baseProjectDataType: 'Data Type',
			baseProjectPurpose: 'Purpose',
			baseProjectCountry: 'Country',
			baseProjectSector: 'Sector',
			baseProjectCategory: 'Category',
			baseProjectDomainProductGroup: 'Product Group',
			baseProjectPeriodicity: 'Periodicity',
			baseProjectType: 'BP Type',
			baseProjectSubType: 'BP Sub Type',
			baseProjectUsers: 'BP Users',
			baseProjectIds: 'BP Ids',
			qcProjectIds: 'QC Ids',
			baseProjectHistory:'Date Range'
		};

		for (const [key, label] of Object.entries(filterMapping)) {
			if (this.selectedFilters[key]?.length) {
				if(key==='baseProjectStartDate' || key==='baseProjectEndDate'){
					this.chips.push({ label, value: 1 });
				}
				else{
					if(key!=='baseProjectIds' && key!='qcProjectIds')
						this.chips.push({ label, value: this.selectedFilters[key].length });
					else
						this.chips.push({ label, value: this.selectedFilters[key].split(',').filter(x => x.trim() !== '').length });
				}
			}
		}

		// Handle the includeDeletedBaseProjects checkbox separately since it's a boolean
		if (this.selectedFilters?.includeDeletedBaseProjects) {
			this.chips.push({ label: 'Deleted Bp\'s' });
		}
	}

	onPageChangeBaseProjects(event: PageChange) {
		this.currentPage = event.pageIndex;
		localStorage.setItem('baseProjectPageNo', JSON.stringify(this.currentPage));
		this.pageSize = event.pageSize;
		this.visibleBaseProjects = this.getPageBaseProjects(this.currentPage, this.pageSize);
		this.cdRef.markForCheck();
	}

	private calculatePageStart(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEnd(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageBaseProjects(page: number, size: number) {
		const start = this.calculatePageStart(page, size);
		const end = this.calculatePageEnd(page, size);
		return this.baseProjectData.slice(start, end);
	}

	removeLocalStorageItems(){
		localStorage.removeItem('previousUrl');
		localStorage.removeItem('selectedFiltersonNavigation');
		localStorage.setItem('baseProjectPageNo', JSON.stringify(1));
	}

	rowsSelectedHandler(event: any, baseProjectData: any){
		const selectedBP: any = [];
		baseProjectData.forEach((item: any) => {
			event.detail.forEach((detailItem: any) => {
				if (detailItem == item.id) {
					selectedBP.push(item);
				}
			})
		});
		const noDistributorSelected = selectedBP.every((bp: any) => bp.qcProjects !== null);
		const allDeletedBPSeleted = selectedBP.every((bp: any) => bp.deleted == true);
		if(noDistributorSelected){
			this.disableAddUserBtn = false;
		}
		else{
			this.disableAddUserBtn = true;
		}
		if(allDeletedBPSeleted){
			this.allDeletedBPSelected = true;
		}
		else{
			this.allDeletedBPSelected = false;
		}
	}

	getUsersList(){
		this.users$ = this.getApiService.getURMUsers({
			userName: '',
			firstName: 'a*',
			lastName: '',
			clientId: 148,
			excludeLockedUsers: true,
			filterUsersByRole: false,
			roleFilter: '',
			pageSize: 50
		}).pipe(
			map((users: any) =>
				users.map((d) => ({
					name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')',
					id: d.userId,
					userName: d.userName,
					fullName: d.firstName + ' ' + d.lastName,
					email: d.eMail
				}))
			)
		);
		this.users$.pipe(takeUntil(this.destroy$)).subscribe({
			next: (result) => {
				this.userList = result;
			}
		});
	}

	copySelectedBaseProjectIds(): void {
		const selectedIds = this.getSelectedBaseProjectIds();
		const idString = selectedIds.join(', ');
	  
		navigator.clipboard.writeText(idString).then(() => {
		  this.notifyWidget('Copied to Clipboard', 'success', `BP IDs: ${idString}`);
		}).catch(err => {
		  this.notifyWidget('Failed to Copy', 'error', 'Could not copy BP IDs to clipboard.');
		});
	}
	  

	userkey(val: any): void {
		if (typeof val != 'object') {
			clearTimeout(this.userSearchInterval);
			this.userSearchInterval = setTimeout(() => {
				const params = {
					userName: val.length > 1 ? val+'*' : '',
					firstName: val ? val+'*' : 'a*',
					lastName: val ? val+'*' : 'a*',
					clientId: 148,
					excludeLockedUsers: true,
					filterUsersByRole: false,
					roleFilter: '',
					pageSize: 500
				};
				this.users$ = this.getApiService.getURMUsers(params).pipe(
					map((users: any) =>
						users.map((d) => ({
							name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')',
							id: d.userId,
							userName: d.userName,
							fullName: d.firstName + ' ' + d.lastName,
							email: d.eMail
						}))
					)
				);
				this.users$.pipe(takeUntil(this.destroy$)).subscribe({
					next: (result: any) => {
						this.userList = result;
					}
				});
			}, 500);
		}
	}

	openAddQCSecurityUsersModal() {
		this.addQCSecurityUsersFormInitialization();
	}

	addQCSecurityUsersFormInitialization(){
		this.addQCSecurityUsersForm = this.formBuilder.group({
			users: [[], Validators.required]
		});
		this.addQCSecurityUsersModal = true;
	}

	addQCSecurityUsers(confirmsave?: boolean) {
		if (!confirmsave) {
			this.resetModal();
		}
		else{
			this.loadBaseProjectData = true;
			const assignedUsers = this.getAssignedUserIds();
			const selectedProjectIds = this.getSelectedProjectIds();
			const payload = {
        		projectTypeId: 2,
				userIds: assignedUsers,
				projectIds: selectedProjectIds
			};
			this.postApiService.addUsersForQCSecurity(payload).subscribe({
				next: (response: any) => this.handleSuccessResponse(response),
				error: (error) => this.handleErrorResponse(error),
				complete: () => this.handleCompleteResponse()
			});
		}
	}

  //BP Security-----------------

  openAddBPSecurityUsersModal() {
		this.addBPSecurityUsersFormInitialization();
	}

	addBPSecurityUsersFormInitialization(){
		this.addBPSecurityUsersForm = this.formBuilder.group({
			users: [[], Validators.required]
		});
		this.addBPSecurityUsersModal = true;
	}

	addBPSecurityUsers(confirmsave?: boolean) {
		if (!confirmsave) {
			this.resetBPModal();
		}
		else{
			this.loadBaseProjectData = true;
			const assignedUsers = this.getAssignedBPUserIds();
			const selectedProjectIds = this.getSelectedBaseProjectIds();
			const payload = {
        		projectTypeId: 1,
				userIds: assignedUsers,
				projectIds: selectedProjectIds
			};
			this.postApiService.addUsersForQCSecurity(payload).subscribe({
				next: (response: any) => this.handleSuccessResponse(response),
				error: (error) => this.handleErrorResponse(error),
				complete: () => this.handleCompleteResponse()
			});
		}
    this.addBPSecurityUsersModal = false;
	}

  resetBPModal() {
		this.addBPSecurityUsersModal = false;
		this.addBPSecurityUsersForm.reset();
	}


	getAssignedBPUserIds() {
		return this.addBPSecurityUsersForm.value.users.map((user: any) => user.id);
	}

  handleCompleteBPResponse() {
		this.getUsersList();
		this.clearSelection();
		this.cdRef.markForCheck();
		this.addBPSecurityUsersModal = false;
		this.loadBaseProjectData = false;
	}

  //BP Security-----------------


	resetModal() {
		this.addQCSecurityUsersModal = false;
		this.addQCSecurityUsersForm.reset();
	}

	getSelectedProjectIds() {
		return this.selectedEntities
			.filter(item => item.qcprojectId)
			.map(item => item.qcprojectId);
	}

	getAssignedUserIds() {
		return this.addQCSecurityUsersForm.value.users.map((user: any) => user.id);
	}

	handleSuccessResponse(response: any) {
		if(response.projectResponses.length > 1){
			this.handleResponseForMultipleUser(response)
		}
		else{
			this.handleResponseForSingleUser(response)
		}
		this.loadBaseProjectData = false;
	}

	handleResponseForSingleUser(response: any) {
		let title = '';
		let message = '';
		if(response.projectResponses.find(res => res.statusMsg.includes('All users assigned successfully'))){
			title = 'Users Assigned Successfully.';
			this.notifyWidget(title, 'success');
		}
		else if (response.projectResponses.find(res => res.statusMsg.includes('duplicate/master users skipped'))) {
			title = 'Users Assigned Successfully.';
			message = 'Note: Duplicate / Master users skipped';
			this.notifyWidget(title, 'success', message);
		}
		else if (response.projectResponses.find(res => res.statusMsg.includes("Either you don't have permission"))) {
			title = "Master Role user is auto-assigned and can't be added manually.";
			this.notifyWidget(title, 'warn');
		}
		else{
			title = 'Error: User already exists.';
			this.notifyWidget(title, 'error');
		}
	}

	handleResponseForMultipleUser(response: any) {
		let title = '';
		let message = '';
		if(response.projectResponses.every(res => res.statusMsg.includes('All users assigned successfully'))){
			title = 'Users Assigned Successfully.';
			this.notifyWidget(title, 'success');
		}
		else if(response.projectResponses.every(res => res.statusMsg.includes('User(s) already assigned'))){
			title = 'Error: User already exists.';
			this.notifyWidget(title, 'error');
		}
		else if(response.projectResponses.every(res => res.statusMsg.includes('You do not have permission'))){
			UserService.forbiddenError = true;
			title = 'Operation Restricted';
			message = 'Insufficient permission for selected BP Country';
			this.notifyWidget(title, 'error', message);
		}
		else if(response.projectResponses.find(res => res.statusMsg.includes('duplicate/master users skipped') ||
			res.statusMsg.includes('already assigned') ||
			res.statusMsg.includes('You do not have permission')
		)){
			if(response.projectResponses.find(res => res.statusMsg.includes('You do not have permission'))){
				title = 'Users Assigned Successfully.';
				message = 'Note: Duplicate / master or Country Restricted users skipped';
				this.notifyWidget(title, 'success', message);
			}
			else{
				title = 'Users Assigned Successfully.';
				message = 'Note: Duplicate / Master users skipped';
				this.notifyWidget(title, 'success', message);
			}
		}
	}

	handleErrorResponse(error: any) {
		let title = '';
		let message = '';
		switch (error.status) {
			case 400:
				title = error.error.value == 'User already exists for this QCProjectId'
					? 'Error: User already exists.'
					: 'Error: ' + error.status + '. Users assigning failed. Please contact administrator.';
				break;
			case 401:
			case 404:
			case 500:
			case 502:
			case 503:
			case 504:
				title = 'Error: ' + error.status + '. Users assigning failed. Please contact administrator.';
				break;
			case 403:
				UserService.forbiddenError = true;
				title = 'Operation Restricted';
				message = 'Insufficient permission for selected BP Country';
				break;
			default:
				title = error?.message == null ?  error?.error : error?.message;
				break;
		}

		this.notifyWidget(title, 'error', message);
		this.addQCSecurityUsersModal = false;
		this.loadBaseProjectData = false;
	}

	handleCompleteResponse() {
		this.getUsersList();
		this.clearSelection();
		this.cdRef.markForCheck();
		this.addQCSecurityUsersModal = false;
		this.loadBaseProjectData = false;
	}

	openCreateBulkBPModal() {
		this.createBulkBPModal = true;
		const selectedIds = this.getSelectedBaseProjectIds();
		this.selectedBPIdsForBulkCreation = selectedIds.join(', ');
	}

	createBulkBaseProjects(confirmCreation?: boolean): void {
		if (!confirmCreation) {
			this.createBulkBPModal = false;
		}
		else{
			this.loadBaseProjectData = true;
			this.createBulkBPModal = false;
			const bpIds = this.selectedBPIdsForBulkCreation.split(',').map(num => parseInt(num.trim()));
			this.postApiService.createBulkBaseProjects({ baseProjectIds: bpIds }).subscribe({
				next: (response: any) => this.handleCreateBulkSuccessResponse(response),
				error: (error) => this.handleCreateBulkBPError(error),
			});
		}
	}

	handleCreateBulkSuccessResponse(response: any){
		const title = 'Bulk Base Project Created';
		this.notifyWidget(title, 'success');
		this.loadBaseProjectData = false;
		this.copiedBPModal = true;
		// Store the original response for table display and create mapped list for copying
		this.copiedBaseProjectList = response.map((item: any) => ({
			...item,
			// Keep original source data and add copied data as baseProject fields for copying
			sourceBaseProjectId: item.baseProjectId,
			sourceBaseProjectName: item.baseProjectName,
			baseProjectId: item.copiedBaseProjectId || item.baseProjectId,
			baseProjectName: item.copiedBaseProjectName || item.baseProjectName
		}));
		this.getBaseProjectsList();
		this.clearSelection();
	}

	handleCreateBulkBPError(error: any): void {
		const errorCodes = [400, 401, 500, 502, 503, 504];
		if (errorCodes.includes(error.status)) {
			const title = `Error: ${error.status}. Unable to create bulk Base Project(s). Please contact administrator.`;
			this.notifyWidget(title, 'error');
		}
		this.loadBaseProjectData = false;
	}

	closeCopiedBPModal(){
		this.copiedBPModal = false;
	}

	copyBPIdsToClipboard(): void {
		const ids = this.copiedBaseProjectList.map(item => item.baseProjectId).join(', ');
		navigator.clipboard.writeText(ids)
		.then(() => {
				this.notifyWidget('Success!', 'success', 'Copied BP IDs to clipboard.');
		})
		.catch(() => {
			this.notifyWidget('Copy Failed', 'error', 'Could not copy BP IDs to clipboard.');
		});
		this.copiedBPModal = false;
	}

	copyBPNamesToClipboard(): void {
		const names = this.copiedBaseProjectList
			.map(item => item.baseProjectName)
			.filter(name => name) // Filter out undefined/null names
			.join(', ');

		if (names) {
			navigator.clipboard.writeText(names)
			.then(() => {
					this.notifyWidget('Success!', 'success', 'Copied BP Names to clipboard.');
			})
			.catch(() => {
				this.notifyWidget('Copy Failed', 'error', 'Could not copy BP Names to clipboard.');
			});
		} else {
			this.notifyWidget('No Names Available', 'warning', 'No base project names available to copy.');
		}
		this.copiedBPModal = false;
	}

	openEditBulkBPModal(){
		this.editBulkBPForm.reset();
		const allSamePanelOrPeriodicity = this.checkPanelOrPeriodicityForDataTypeList(this.selectedEntities);
		this.selectedPeriodicityName = allSamePanelOrPeriodicity.periodicityName;
		this.filteredDataTypeData = this.handleDataTypeDropdownListForEdit(
			allSamePanelOrPeriodicity ? allSamePanelOrPeriodicity.panelId : '', 
			allSamePanelOrPeriodicity ? allSamePanelOrPeriodicity.periodicityId : this.selectedEntities[0]?.periodicityId
		);
		this.editBulkBPModal = true;
	}

	editBulkBPFormInitialization(){
		this.editBulkBPForm = this.formBuilder.group({
			dataTypeId: null,
			purposeId: null,
			suffix: ['', Validators.maxLength(40)]
		});
		this.editBulkBPForm.valueChanges.subscribe(value => {
			const hasValidValue = value.dataTypeId !== null ||
								value.purposeId !== null ||
								(value.suffix && value.suffix.trim() !== '');
								
			this.disableEditBtn = !hasValidValue;
		});	
	}

	handleDataTypeDropdownListForEdit(panelId?: any, periodicityValue?: number){
		let valuesToFilter: string[];
		if(panelId == 3){
			valuesToFilter = ['5', '6', '7'];
		}
		else{
			valuesToFilter = [];
		}
		if(panelId != 3 && periodicityValue){
			if (periodicityValue === 2) {
				valuesToFilter = ['1', '2', '4', '5', '6', '7', '9', '10'];
			}
			else if (periodicityValue === 4) {
				valuesToFilter = ['3', '4', '7', '9', '11'];
			}
			else {
				valuesToFilter = ['4', '7', '9', '11'];
			}
		}
		const filteredData = this.dataTypeData.filter((item: AutocompleteOption) => valuesToFilter.includes(item.value));
		return this.sortDataTypes(filteredData);
	}

	checkPanelOrPeriodicityForDataTypeList(entities: { panelId: number; periodicityName: string; periodicityId: number }[]): {
		panelId: number | null;
		periodicityName: string | null;
		periodicityId: number | null;
	} {
		if (entities.length === 0) {
			return { panelId: null, periodicityName: null, periodicityId: null };
		}

		const allPanelThree = entities.every(e => e.panelId === 3);
		const { periodicityName, periodicityId } = entities[0];

		if (allPanelThree) {
			return { panelId: 3, periodicityName, periodicityId };
		}

		const allSamePeriodicity = entities.every(e => e.periodicityName === periodicityName && e.periodicityId === periodicityId);

		if (allSamePeriodicity) {
			return { panelId: null, periodicityName, periodicityId };
		}

		return { panelId: null, periodicityName: null, periodicityId: null };
	}

	editBulkBP(confirmEditBulkBP?: boolean): void{
		if (!confirmEditBulkBP) {
			this.editBulkBPModal = false;
		}
		else{
			this.loadBaseProjectData = true;
			this.putApiService.updateBulkBaseProject(
				this.selectedEntities.map(item => item.id),
				this.editBulkBPForm?.get('dataTypeId')?.value ? parseInt(this.editBulkBPForm?.get('dataTypeId')?.value) : 0,
				this.editBulkBPForm?.get('purposeId')?.value ? parseInt(this.editBulkBPForm?.get('purposeId')?.value) : 0,
				this.editBulkBPForm?.get('suffix')?.value
			).subscribe({
				next: () => this.handleUpdateBulkBaseProjectSuccess(),
				error: (error) => this.handleUpdateBulkBaseProjectError(error)
			});
		}
	}

	handleUpdateBulkBaseProjectSuccess(){
		const title = 'Bulk Edit Base Projects Successful';
		this.notifyWidget(title, 'success');
		this.loadBaseProjectData = false;
		this.editBulkBPModal = false;
		this.editBulkBPForm.reset();
		this.getBaseProjectsList();
		this.clearSelection();
	}

	handleUpdateBulkBaseProjectError(error: any): void {
		const errorCodes = [400, 401, 500, 502, 503, 504];
		if (errorCodes.includes(error.status)) {
			const title = `Error: ${error.status}. Unable to update bulk Base Project(s). Please contact administrator.`;
			this.notifyWidget(title, 'error');
		}
		this.editBulkBPForm.reset();
		this.loadBaseProjectData = false;
		this.editBulkBPModal = false;
	}

	getOffsetAtTime(timezone, date) {
		const zone = moment.tz.zone(timezone);
		if (zone) {
			const timestamp = moment.utc(date).valueOf(); // Convert date to UTC timestamp
			const abbrs = zone.abbrs; // Get the list of abbreviations
			const untils = zone.untils; // Get the list of timestamp changes (for daylight saving time changes)
			
			// Find the correct abbreviation based on the timestamp
			for (let i = 0; i < untils.length; i++) {
				if (timestamp < untils[i]) {
				return abbrs[i]; // Return abbreviation if timestamp is before the DST change
				}
			}
			
			// If no matching change is found (for times after the last DST change), use the last abbreviation
			return abbrs[abbrs.length - 1]; // Return the last abbreviation
		} 
		else {
			return null; // Return null if the timezone is not found
		}
	}

	notifyWidget(title: string, notificationType: string, message?: string): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if (notificationType == 'warn') {
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.WARNING,
				message: message || ''
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}

}
