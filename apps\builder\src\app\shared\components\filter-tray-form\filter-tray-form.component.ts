import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, inject } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import * as moment from 'moment-timezone';
import { Observable, Subject, Subscription, map } from 'rxjs';

export interface AutocompleteOption {
	value: string;
	label: string;
}

@Component({
	selector: 'dx-filter-tray-form',
	templateUrl: './filter-tray-form.component.html',
	styleUrls: ['./filter-tray-form.component.scss'],
})
export class FilterTrayFormComponent implements OnInit, OnDestroy {
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);

	sectors$!: Observable<AutocompleteOption[]>;
	categories$!: Observable<AutocompleteOption[]>;
	dataType$!: Observable<AutocompleteOption[]>;
	purpose$!: Observable<AutocompleteOption[]>;
	productGroup$!: Observable<AutocompleteOption[]>;
	userEmails$!: Observable<AutocompleteOption[]>;
	userStatusList$!: Observable<AutocompleteOption[]>;
	filterFG!: FormGroup;
	subscription!: Subscription | undefined;
	userRoleSubscription!: Subscription | undefined;
	sectorsList: any = [];
	categoriesList: any = [];
	productGroupData: any = [];
	domainProductGroupData: any = [];
	userEmailList: any = [];
	userStatusList: any = [];
	data!: any;
	userRole: any;
	retailerSeparationStatusList: any = [];
	timezone = moment.tz.guess();

	@Input() sliceName!: string;
	@Input() getFilterData!: Subject<boolean>;
	@Input() resetForm!: Subject<boolean>;
	@Input() panelList!: any;
	@Input() countriesList!: any;
	@Input() periodicityData!: any;
	@Input() dataTypeData!: any;
	@Input() purposeData!: any;
	@Input() projectSubTypeList!: any;
	@Input() userRolesList!: any;
	@Input() baseProjectUserList!: any
	@Output() selectedFilters = new EventEmitter();
	filtersFromNavigation: any;
	dateIntervalList: any = [];
	bpHistory: any;

	constructor(
		private formBuilder: FormBuilder, 
		private getApiService: GetApiService,
		private postApiService: PostApiService,
		private router: Router
	) {}

	ngOnInit() {
		this.dateIntervalList = [
			{ label: 'Today', value: '1' },
			{ label: 'Last 3 days', value: '2' },
			{ label: 'Last Week', value: '3' },
			{ label: 'Last Month', value: '4' },
			{ label: 'Last 3 Months', value: '5' },
			{ label: 'Last 6 Months', value: '6' },
			{ label: 'Last Year', value: '7' },
		];
		this.loadFilterTrayDropdownData();
		this.filterFGInitializer();
		this.loadFilterTrayFormData();
		this.filterFormValueChangeSubscriptions();
		this.resetFilterTraySubscription();
	}

	loadFilterTrayDropdownData() {
		if (this.router.url !== '/settings/user-role-list') {
			this.userRoleSubscription = this.getApiService.getUserRoleSubject().subscribe((userRole) => {
				this.userRole = userRole;
				this.getSectorList();
				this.getCategory();
			});
		}
		else{
			this.getFilterData?.subscribe((value) => {
				if (value) {
					this.getUserEmails();
					this.getUserStatus();
				}
			});
		}
	}
	
	loadFilterTrayFormData() {
		this.filtersFromNavigation = JSON.parse(localStorage.getItem('selectedFiltersonNavigation') || 'null');
		if (this.filtersFromNavigation) {
			this.patchFilterValues(this.filtersFromNavigation);
			if(this.filtersFromNavigation && this.filtersFromNavigation.baseProjectCountry.length){
				this.getDomainProductGroup(this.filterFG.get('baseProjectSector')?.value, this.filterFG.get('baseProjectCategory')?.value);
				this.filterFG.get('baseProjectDomainProductGroup')?.enable();
			}
		}
	}
	
	patchFilterValues(filters: any) {
		this.filterFG.patchValue({
			baseProjectPanel: filters.baseProjectPanel,
			baseProjectDataType: filters.baseProjectDataType,
			baseProjectPurpose: filters.baseProjectPurpose,
			baseProjectCountry: filters.baseProjectCountry,
			baseProjectDomainProductGroup: filters.baseProjectDomainProductGroup,
			baseProjectProductGroup: filters.baseProjectProductGroup,
			baseProjectSector: filters.baseProjectSector,
			baseProjectCategory: filters.baseProjectCategory,
			baseProjectPeriodicity: filters.baseProjectPeriodicity,
			baseProjectSubType: filters.baseProjectSubType,
			baseProjectType: filters.baseProjectType,
			baseProjectStartDate: filters.baseProjectStartDate,
			baseProjectEndDate: filters.baseProjectEndDate,
			baseProjectIds: filters.baseProjectIds,
			qcProjectIds: filters.qcProjectIds,
			baseProjectUsers: filters.baseProjectUsers,
			baseProjectHistory: filters.baseProjectHistory,
			includeDeletedBaseProjects: filters.includeDeletedBaseProjects || false 

		});
	}
	
	filterFormValueChangeSubscriptions() {
		this.baseProjectPanelSubscription();
		this.baseProjectCountrySubscription();
		this.baseProjectSectorSubscription();
		this.baseProjectCategorySubscription();
		this.baseProjectHistorySubscription();
		this.baseProjectDPGSubscription();
		this.filterFG.valueChanges.subscribe((value) => {
			this.selectedFilters.emit(value);
		});
	}

	baseProjectPanelSubscription(){
		this.subscription = this.filterFG.get('baseProjectPanel')?.valueChanges.subscribe((values: any) => {
			if (values.length && (this.filterFG.get('baseProjectCountry')?.value || this.filterFG.get('baseProjectSector')?.value || this.filterFG.get('baseProjectCategory')?.value)) {
				this.filterFG.get('baseProjectDomainProductGroup')?.setValue([]);
				this.filterFG.get('baseProjectProductGroup')?.setValue([]);
				this.productGroupData = [];
				this.domainProductGroupData = [];
			}
	
			const hasCountry = this.filterFG.get('baseProjectCountry')?.value.length > 0;
			const hasSectorOrCategory =
				this.filterFG.get('baseProjectSector')?.value.length > 0 ||
				this.filterFG.get('baseProjectCategory')?.value.length > 0;
	
			if (hasCountry || hasSectorOrCategory) {
				this.filterFG.get('baseProjectDomainProductGroup')?.enable();
			} else {
				this.filterFG.get('baseProjectDomainProductGroup')?.disable();
			}
		});
	}

	baseProjectCountrySubscription(){
		this.subscription = this.filterFG.get('baseProjectCountry')?.valueChanges.subscribe((values: any) => {
			if (values.length) {
				this.filterFG.get('baseProjectDomainProductGroup')?.setValue([]);
				this.filterFG.get('baseProjectProductGroup')?.setValue([]);
				this.productGroupData = [];
				this.domainProductGroupData = [];
				this.getDomainProductGroup(
					this.filterFG.get('baseProjectSector')?.value,
					this.filterFG.get('baseProjectCategory')?.value
				);
			}
	
			const hasCountry = values.length > 0;
			const hasSectorOrCategory =
				this.filterFG.get('baseProjectSector')?.value.length > 0 ||
				this.filterFG.get('baseProjectCategory')?.value.length > 0;
	
			if (hasCountry || hasSectorOrCategory) {
				this.filterFG.get('baseProjectDomainProductGroup')?.enable();
			} else {
				this.filterFG.get('baseProjectDomainProductGroup')?.disable();
			}
		});
	}

	baseProjectSectorSubscription(){
		if (this.router.url !== '/settings/user-role-list') {
			this.subscription = this.filterFG.get('baseProjectSector')?.valueChanges.subscribe((values: any) => {
				this.filterFG.get('baseProjectDomainProductGroup')?.setValue([]);
				this.filterFG.get('baseProjectProductGroup')?.setValue([]);
				this.filterFG.get('baseProjectCategory')?.setValue([]);
				this.productGroupData = [];
				this.domainProductGroupData = [];
				this.getCategory(values);	
				const hasCountry = this.filterFG.get('baseProjectCountry')?.value.length > 0;
				const hasSectorOrCategory =
					values.length > 0 ||
					this.filterFG.get('baseProjectCategory')?.value.length > 0;
		
				if (hasCountry || hasSectorOrCategory) {
					this.filterFG.get('baseProjectDomainProductGroup')?.enable();
				} else {
					this.filterFG.get('baseProjectDomainProductGroup')?.disable();
				}
			});
		}
	}
	
	baseProjectHistorySubscription(){
		this.subscription = this.filterFG.get('baseProjectHistory')?.valueChanges.subscribe((values: any) => {
			this.bpHistory=values;
			
		});
	}

	baseProjectCategorySubscription(){
		if (this.router.url !== '/settings/user-role-list') {
			this.subscription = this.filterFG.get('baseProjectCategory')?.valueChanges.subscribe((values: any) => {
				this.filterFG.get('baseProjectDomainProductGroup')?.setValue([]);
				this.filterFG.get('baseProjectProductGroup')?.setValue([]);
				this.productGroupData = [];
				this.domainProductGroupData = [];
				this.getDomainProductGroup(
					this.filterFG.get('baseProjectSector')?.value,
					values
				);
		
				const hasCountry = this.filterFG.get('baseProjectCountry')?.value?.length > 0;
				const hasSectorOrCategory =
					this.filterFG.get('baseProjectSector')?.value?.length > 0 ||
					values.length > 0;
		
				if (hasCountry || hasSectorOrCategory) {
					this.filterFG.get('baseProjectDomainProductGroup')?.enable();
				} else {
					this.filterFG.get('baseProjectDomainProductGroup')?.disable();
				}
			});
		}
	}

	baseProjectDPGSubscription(){
		if (this.router.url !== '/settings/user-role-list') {
			this.subscription = this.filterFG.get('baseProjectDomainProductGroup')?.valueChanges.subscribe((values: any) => {
				if(values && values.length){
					this.getProductGroup(
						this.filterFG.get('baseProjectPanel')?.value,
						this.filterFG.get('baseProjectCountry')?.value,
						this.filterFG.get('baseProjectSector')?.value,
						this.filterFG.get('baseProjectCategory')?.value,
						values,
					);
				} else {
					this.filterFG.get('baseProjectProductGroup')?.setValue([]);
				}
			});
		}
	}

	resetFilterTraySubscription() {
		this.resetForm?.subscribe((value) => {
			if (value) {
				this.filterFG.reset();
			}
		});
	}

	ngOnDestroy() {
		this.subscription?.unsubscribe();
		this.userRoleSubscription?.unsubscribe();
	}

	filterFGInitializer(){
		this.filterFG = this.formBuilder.group({
			baseProjectPanel: [[], { nonNullable: true }],
			baseProjectDataType: [[], { nonNullable: true }],
			baseProjectPurpose: [[], { nonNullable: true }],
			baseProjectCountry: [[], { nonNullable: true }],
			baseProjectDomainProductGroup: [[], { nonNullable: true }],
			baseProjectProductGroup: [[], { nonNullable: true }],
			baseProjectSector: [[], { nonNullable: true }],
			baseProjectCategory: [[], { nonNullable: true }],
			baseProjectPeriodicity: [[], { nonNullable: true }],
			baseProjectSubType: [[], { nonNullable: true }],
			baseProjectType: [[], { nonNullable: true }],
			email: [[], { nonNullable: true }],
			userRoleRequested: [[], { nonNullable: true }],
			status: [[], { nonNullable: true }],
			baseProjectStartDate: null,
			baseProjectEndDate: null,
			baseProjectIds:null,
			qcProjectIds: null,
			baseProjectUsers:null,
			baseProjectHistory: [[], { nonNullable: true }],
			includeDeletedBaseProjects: [false, { nonNullable: true }], 
			userRoleCountry: [[], { nonNullable: true }]
		});
		this.filterFG.get('baseProjectDomainProductGroup')?.disable();
	}

	getUserEmails(){
		this.userEmails$ = this.getApiService.getAsyncUserEmails()
			.pipe(
				map((emaiList: any) => emaiList.map((email: any) => ({ label: email, value: email } as AutocompleteOption)))
			);
		this.userEmails$.subscribe({
			next: (result) => {
				this.userEmailList = result;
			}
		});
	}

	getUserStatus(){
		this.userStatusList$ = this.getApiService.getAsyncUserStatus()
			.pipe(
				map((statusList: any) => statusList.map((status: any) => ({ label: status.name, value: (status.id).toString() } as AutocompleteOption)))
			);
		this.userStatusList$.subscribe({
			next: (result) => {
				this.userStatusList = result;
			}
		});
	}

	getSectorList(){
		this.sectors$ = this.getApiService.getlmxApiSectors().pipe(
			map(
				(sectors: any) =>
					sectors
						.map(
							(sector: any) =>
								({
									label: sector.name, // Use label instead of name
									value: sector.id.toString(),
								} as AutocompleteOption)
						)
						.sort((a, b) => a.label.localeCompare(b.label)) // Use label instead of name
			)
		);

		this.sectors$.subscribe({
			next: (result) => {
				this.sectorsList = result;
			}
		});
	}

	getCategory(sector?: any) {
		this.categories$ = this.postApiService.getlmxApiCategories(sector).pipe(
			map(
				(categories: any) =>
					categories
						.map(
							(categories: any) =>
								({
									label: categories.name, // Use label instead of name
									value: categories.id.toString(),
								} as AutocompleteOption)
						)
						.sort((a, b) => a.label.localeCompare(b.label)) // Use label instead of name
			)
		);

		this.categories$.subscribe({
			next: (result) => {
				this.categoriesList = result;
			}
		});
	}

	getDomainProductGroup(sector?: any, category?: any) {
		this.productGroup$ = this.postApiService.getAsyncDomainProductGroup(sector, category).pipe(
			map((productGroups: any) =>
				productGroups.map((productGroup: any) => ({
					label: productGroup.name + ' (' + productGroup.id + ')',
					value: productGroup.id.toString(),
				}))
			)
		);
		this.productGroup$.subscribe({
			next: (result) => {
				this.domainProductGroupData = result;
			},
			error: (error) => {
				let title: string;
				if(error.status == 400 || error.status == 401 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504){
					title = 'Error: ' + error.status + '. Unable to fetch Product Groups. Please contact administrator.';
					this.notifyWidget(title, 'error');
				}
			},
		});
	}

	getProductGroup(panel: any, country: any, sector: any, category: any, domainProductGroup: any) {
		if(country.length || sector.length || category.length){
			this.productGroup$ = this.postApiService.getAsyncProductGroup((panel.length) ? panel : [1,2,3], country, sector, category, domainProductGroup).pipe(
				map((productGroups: any) =>
					productGroups.map((productGroup: any) => ({
						label: productGroup.description + ' (' + productGroup.id + ')',
						value: productGroup.id.toString(),
					}))
				)
			);
			this.productGroup$.subscribe({
				next: (result) => {
					const selectedPGs: any = [];
					result.forEach((item: any) => {
						selectedPGs.push(item.value);
					})
					this.filterFG.get('baseProjectProductGroup')?.setValue(selectedPGs);
					this.selectedFilters.emit(this.filterFG.value);
					this.productGroupData = result;
				},
				error: (error) => {
					let title: string;
					if(error.status == 400 || error.status == 401 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504){
						title = 'Error: ' + error.status + '. Unable to fetch Product Groups. Please contact administrator.';
						this.notifyWidget(title, 'error');
					}
				},
			});
		}
	}	

	notifyWidget(title: string, notificationType: string, message?: string): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}
	
	selectedDateFilter(value: any) {
		this.filterFG.get('baseProjectHistory')?.setValue(value.selectedDays);
		this.filterFG.get('baseProjectStartDate')?.setValue(value.startDate ? moment.utc(value.startDate).tz(this.timezone).startOf('day') : null);
		this.filterFG.get('baseProjectEndDate')?.setValue((value.endDate && new Date(value.endDate).toString() !== "Invalid Date") ? moment.utc(value.endDate).tz(this.timezone).endOf('day') : null);
	}
}
